import React, { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import Header from '../components/Layout/Header';
import Footer from '../components/Layout/Footer';

const About: React.FC = () => {
  const { t } = useTranslation();
  const mainRef = useRef<HTMLElement>(null);

  useEffect(() => {
    // Remove navbar spacing - set padding-top to 0
    const removeNavbarSpacing = () => {
      if (mainRef.current) {
        mainRef.current.style.paddingTop = '0rem';
        mainRef.current.style.setProperty('padding-top', '0rem', 'important');
      }
    };

    removeNavbarSpacing();
    window.addEventListener('resize', removeNavbarSpacing);

    return () => window.removeEventListener('resize', removeNavbarSpacing);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main
        ref={mainRef}
        className="no-navbar-spacing"
        style={{
          paddingTop: '0rem'
        }}
      >{/* No navbar spacing - content under navbar */}
        {/* Hero Section */}
        <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 relative overflow-hidden">
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-5xl sm:text-6xl lg:text-7xl font-black text-white mb-8 leading-tight">
              About Us
            </h1>
            <p className="text-xl text-blue-100 mb-12 leading-relaxed max-w-3xl mx-auto">
              Content coming soon...
            </p>
          </div>
        </section>

        {/* Content Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="bg-gray-50 rounded-2xl p-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Page Under Construction</h2>
              <p className="text-lg text-gray-600">
                This page is currently being updated. Please check back soon for new content.
              </p>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default About;