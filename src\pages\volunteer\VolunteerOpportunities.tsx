import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Users, ArrowLeft, MapPin, Clock, Heart, Award, Star, Calendar, CheckCircle } from 'lucide-react';
import Header from '../../components/Layout/Header';
import Footer from '../../components/Layout/Footer';

const VolunteerOpportunities: React.FC = () => {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    skills: [] as string[],
    experience: '',
    availability: [] as string[],
    hoursPerWeek: '',
    duration: '',
    emergencyName: '',
    emergencyPhone: '',
    emergencyRelation: '',
    motivation: '',
    accommodations: '',
    backgroundCheck: false,
    understanding: false,
    emailUpdates: false
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      if (name === 'skills' || name === 'availability') {
        setFormData(prev => ({
          ...prev,
          [name]: checked
            ? [...prev[name as keyof typeof prev] as string[], value]
            : (prev[name as keyof typeof prev] as string[]).filter(item => item !== value)
        }));
      } else {
        setFormData(prev => ({ ...prev, [name]: checked }));
      }
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Here you would typically send the data to your backend
    console.log('Form submitted:', formData);
    setIsSubmitted(true);

    // Scroll to top to show success message
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };
  // Mock volunteer opportunities data
  const opportunities = [
    {
      id: 1,
      title: "Emergency Response Team",
      location: "Citywide",
      timeCommitment: "4-8 hours/week",
      urgency: "High",
      description: "Join our rapid response team to provide immediate assistance during emergencies and disasters.",
      skills: ["First Aid", "Communication", "Physical Fitness"],
      volunteers: 45,
      needed: 20
    },
    {
      id: 2,
      title: "Community Preparedness Educator",
      location: "Local Schools",
      timeCommitment: "2-4 hours/week",
      urgency: "Medium",
      description: "Teach community members about disaster preparedness and emergency planning.",
      skills: ["Public Speaking", "Teaching", "Emergency Knowledge"],
      volunteers: 12,
      needed: 8
    },
    {
      id: 3,
      title: "Disaster Relief Coordinator",
      location: "Regional",
      timeCommitment: "6-10 hours/week",
      urgency: "High",
      description: "Coordinate relief efforts and manage volunteer teams during disaster response operations.",
      skills: ["Leadership", "Organization", "Crisis Management"],
      volunteers: 8,
      needed: 5
    }
  ];

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'High': return 'bg-red-100 text-red-800 border-red-200';
      case 'Medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="navbar-spacing">
        {/* Hero Section */}
        <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-blue-700 via-blue-800 to-indigo-800 relative overflow-hidden">
          <div className="absolute inset-0">
            <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-400/8 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-indigo-300/12 rounded-full blur-3xl animate-pulse delay-1000"></div>
          </div>

          <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="inline-flex items-center px-6 py-3 rounded-full bg-blue-600/20 backdrop-blur-xl border border-blue-400/30 text-sm font-semibold mb-8 text-blue-100 shadow-lg">
              <Users size={16} className="mr-2 text-blue-300" />
              Volunteer Opportunities
            </div>

            <h1 className="text-3xl sm:text-4xl lg:text-6xl font-bold text-white mb-8 leading-tight drop-shadow-xl">
              Find Your Way to
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-100 via-blue-200 to-indigo-100">
                Make a Difference
              </span>
            </h1>

            <p className="text-lg sm:text-xl text-blue-50 mb-10 max-w-3xl mx-auto leading-relaxed drop-shadow-lg">
              Discover meaningful volunteer opportunities that match your skills and schedule. Every contribution helps build stronger, more resilient communities.
            </p>

            <Link
              to="/"
              className="inline-flex items-center text-blue-200 hover:text-white transition-colors duration-300 mb-6"
            >
              <ArrowLeft size={20} className="mr-2" />
              Back to Home
            </Link>
          </div>
        </section>

        {/* Volunteer Registration Section */}
        <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-gray-50 to-blue-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12 sm:mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Join Our Volunteer Network
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
                Register today to receive notifications about volunteer opportunities that match your skills and availability.
              </p>
            </div>

            {/* Success Message */}
            {isSubmitted && (
              <div className="bg-green-50 border border-green-200 rounded-2xl p-6 mb-8">
                <div className="flex items-center justify-center mb-4">
                  <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                    <CheckCircle size={24} className="text-white" />
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-green-800 text-center mb-2">
                  Registration Successful!
                </h3>
                <p className="text-green-700 text-center">
                  Thank you for registering as a volunteer. We'll review your application and contact you within 3-5 business days with next steps.
                </p>
              </div>
            )}

            {/* Registration Form */}
            <div className="bg-white rounded-3xl shadow-xl p-6 sm:p-8 lg:p-12 border border-blue-100">
              <form onSubmit={handleSubmit} className="space-y-8">
                {/* Personal Information */}
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                    <Users size={20} className="mr-3 text-blue-600" />
                    Personal Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        First Name *
                      </label>
                      <input
                        type="text"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                        placeholder="Enter your first name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Last Name *
                      </label>
                      <input
                        type="text"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                        placeholder="Enter your last name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Email Address *
                      </label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number *
                      </label>
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                        placeholder="(*************"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Address
                      </label>
                      <input
                        type="text"
                        name="address"
                        value={formData.address}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                        placeholder="Street address, city, state, zip code"
                      />
                    </div>
                  </div>
                </div>

                {/* Skills and Experience */}
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                    <Award size={20} className="mr-3 text-blue-600" />
                    Skills & Experience
                  </h3>
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        Select your skills (check all that apply):
                      </label>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {['First Aid/CPR', 'Emergency Response', 'Communication', 'Leadership', 'Teaching', 'Technology', 'Construction', 'Medical', 'Transportation', 'Languages', 'Logistics', 'Counseling'].map((skill) => (
                          <label key={skill} className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-colors duration-200 cursor-pointer">
                            <input
                              type="checkbox"
                              name="skills"
                              value={skill}
                              checked={formData.skills.includes(skill)}
                              onChange={handleInputChange}
                              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                            <span className="ml-3 text-sm text-gray-700">{skill}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Previous volunteer experience
                      </label>
                      <textarea
                        rows={4}
                        name="experience"
                        value={formData.experience}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                        placeholder="Describe any previous volunteer work or relevant experience..."
                      />
                    </div>
                  </div>
                </div>

                {/* Availability */}
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                    <Clock size={20} className="mr-3 text-blue-600" />
                    Availability
                  </h3>
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        When are you available? (check all that apply):
                      </label>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                        {['Weekday Mornings', 'Weekday Afternoons', 'Weekday Evenings', 'Weekend Mornings', 'Weekend Afternoons', 'Weekend Evenings', 'Emergency Response', 'Flexible Schedule'].map((time) => (
                          <label key={time} className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-colors duration-200 cursor-pointer">
                            <input
                              type="checkbox"
                              name="availability"
                              value={time}
                              checked={formData.availability.includes(time)}
                              onChange={handleInputChange}
                              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                            <span className="ml-3 text-sm text-gray-700">{time}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Hours per week you can commit
                        </label>
                        <select
                          name="hoursPerWeek"
                          value={formData.hoursPerWeek}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                        >
                          <option value="">Select hours per week</option>
                          <option value="1-2">1-2 hours</option>
                          <option value="3-5">3-5 hours</option>
                          <option value="6-10">6-10 hours</option>
                          <option value="11-20">11-20 hours</option>
                          <option value="20+">20+ hours</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Preferred volunteer duration
                        </label>
                        <select
                          name="duration"
                          value={formData.duration}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                        >
                          <option value="">Select duration</option>
                          <option value="short-term">Short-term (1-3 months)</option>
                          <option value="medium-term">Medium-term (3-6 months)</option>
                          <option value="long-term">Long-term (6+ months)</option>
                          <option value="ongoing">Ongoing commitment</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Emergency Contact */}
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                    <Heart size={20} className="mr-3 text-blue-600" />
                    Emergency Contact
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Emergency contact name *
                      </label>
                      <input
                        type="text"
                        name="emergencyName"
                        value={formData.emergencyName}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                        placeholder="Full name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Emergency contact phone *
                      </label>
                      <input
                        type="tel"
                        name="emergencyPhone"
                        value={formData.emergencyPhone}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                        placeholder="(*************"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Relationship to emergency contact
                      </label>
                      <input
                        type="text"
                        name="emergencyRelation"
                        value={formData.emergencyRelation}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                        placeholder="e.g., Spouse, Parent, Sibling, Friend"
                      />
                    </div>
                  </div>
                </div>

                {/* Additional Information */}
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                    <Star size={20} className="mr-3 text-blue-600" />
                    Additional Information
                  </h3>
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Why do you want to volunteer with us?
                      </label>
                      <textarea
                        rows={4}
                        name="motivation"
                        value={formData.motivation}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                        placeholder="Tell us about your motivation to volunteer and help your community..."
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Special accommodations or considerations
                      </label>
                      <textarea
                        rows={3}
                        name="accommodations"
                        value={formData.accommodations}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                        placeholder="Any physical limitations, dietary restrictions, or other considerations we should know about..."
                      />
                    </div>
                  </div>
                </div>

                {/* Agreement */}
                <div className="bg-blue-50 rounded-2xl p-6 border border-blue-100">
                  <div className="space-y-4">
                    <label className="flex items-start">
                      <input
                        type="checkbox"
                        name="backgroundCheck"
                        checked={formData.backgroundCheck}
                        onChange={handleInputChange}
                        required
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mt-1"
                      />
                      <span className="ml-3 text-sm text-gray-700">
                        I agree to undergo a background check if required for certain volunteer positions *
                      </span>
                    </label>
                    <label className="flex items-start">
                      <input
                        type="checkbox"
                        name="understanding"
                        checked={formData.understanding}
                        onChange={handleInputChange}
                        required
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mt-1"
                      />
                      <span className="ml-3 text-sm text-gray-700">
                        I understand that volunteer opportunities are subject to availability and matching with organizational needs *
                      </span>
                    </label>
                    <label className="flex items-start">
                      <input
                        type="checkbox"
                        name="emailUpdates"
                        checked={formData.emailUpdates}
                        onChange={handleInputChange}
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mt-1"
                      />
                      <span className="ml-3 text-sm text-gray-700">
                        I would like to receive email updates about volunteer opportunities and disaster preparedness information
                      </span>
                    </label>
                  </div>
                </div>

                {/* Submit Button */}
                <div className="text-center pt-6">
                  <button
                    type="submit"
                    className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-12 py-4 rounded-2xl text-lg font-semibold hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center mx-auto"
                  >
                    <Heart size={20} className="mr-3" />
                    Register as Volunteer
                    <ArrowLeft size={20} className="ml-3 rotate-180" />
                  </button>
                  <p className="text-sm text-gray-500 mt-4">
                    We'll review your application and contact you within 3-5 business days
                  </p>
                </div>
              </form>
            </div>
          </div>
        </section>

        {/* Available Opportunities Preview */}
        <section className="py-16 sm:py-20 lg:py-24 bg-white">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12 sm:mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Current Volunteer Opportunities
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Explore the types of volunteer opportunities available in our network
              </p>
            </div>

            {/* Opportunities Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
              {opportunities.map((opportunity) => (
                <div key={opportunity.id} className="group bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-2xl hover:border-blue-200 transition-all duration-300 overflow-hidden transform hover:-translate-y-1">
                  <div className="p-8">
                    <div className="flex items-start justify-between mb-6">
                      <h3 className="text-xl font-bold text-gray-900 leading-tight group-hover:text-blue-600 transition-colors duration-300">
                        {opportunity.title}
                      </h3>
                      <span className={`px-3 py-1 rounded-full text-xs font-semibold border ${getUrgencyColor(opportunity.urgency)}`}>
                        {opportunity.urgency}
                      </span>
                    </div>

                    <p className="text-gray-600 mb-6 leading-relaxed">
                      {opportunity.description}
                    </p>

                    <div className="space-y-4 mb-8">
                      <div className="flex items-center text-sm text-gray-600">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                          <MapPin size={14} className="text-blue-600" />
                        </div>
                        <span className="font-medium">{opportunity.location}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                          <Clock size={14} className="text-blue-600" />
                        </div>
                        <span className="font-medium">{opportunity.timeCommitment}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                          <Users size={14} className="text-blue-600" />
                        </div>
                        <span className="font-medium">{opportunity.volunteers} volunteers, {opportunity.needed} more needed</span>
                      </div>
                    </div>

                    <div className="mb-8">
                      <h4 className="text-sm font-semibold text-gray-900 mb-3">Required Skills:</h4>
                      <div className="flex flex-wrap gap-2">
                        {opportunity.skills.map((skill, index) => (
                          <span key={index} className="px-3 py-1 bg-blue-100 text-blue-700 text-xs font-medium rounded-full">
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>

                    <button className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-3 rounded-xl font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 flex items-center justify-center group-hover:shadow-lg">
                      <Heart size={18} className="mr-2" />
                      Express Interest
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-gray-50 to-blue-50">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12 sm:mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Volunteer Benefits
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Volunteering with us comes with meaningful rewards and personal growth opportunities
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Heart size={24} className="text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Make Impact</h3>
                <p className="text-gray-600 text-sm">Directly help communities in need</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Award size={24} className="text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Gain Skills</h3>
                <p className="text-gray-600 text-sm">Develop valuable emergency response skills</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users size={24} className="text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Build Network</h3>
                <p className="text-gray-600 text-sm">Connect with like-minded volunteers</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Star size={24} className="text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Recognition</h3>
                <p className="text-gray-600 text-sm">Receive certificates and awards</p>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default VolunteerOpportunities;
