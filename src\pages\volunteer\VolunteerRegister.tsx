import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Heart, ArrowLeft, Users, Award, Star, CheckCircle } from 'lucide-react';
import Header from '../../components/Layout/Header';
import Footer from '../../components/Layout/Footer';

const VolunteerRegister: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="navbar-spacing">
        {/* Hero Section */}
        <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-blue-700 via-blue-800 to-indigo-800 relative overflow-hidden">
          <div className="absolute inset-0">
            <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-400/8 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-indigo-300/12 rounded-full blur-3xl animate-pulse delay-1000"></div>
          </div>

          <div className="relative max-w-4xl mx-auto px-6 lg:px-8 text-center">
            <div className="inline-flex items-center px-6 py-3 rounded-full bg-blue-600/20 backdrop-blur-xl border border-blue-400/30 text-sm font-semibold mb-6 text-blue-100 shadow-lg">
              <Heart size={16} className="mr-2 text-blue-300" />
              Volunteer Registration
            </div>

            <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight drop-shadow-xl">
              Become a
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-100 via-blue-200 to-indigo-100">
                Community Hero
              </span>
            </h1>

            <p className="text-xl text-blue-50 mb-12 max-w-3xl mx-auto leading-relaxed drop-shadow-lg">
              Join our network of dedicated volunteers who are making a real difference in disaster response and community resilience.
            </p>

            <Link
              to="/"
              className="inline-flex items-center text-blue-200 hover:text-white transition-colors duration-300 mb-8"
            >
              <ArrowLeft size={20} className="mr-2" />
              Back to Home
            </Link>
          </div>
        </section>

        {/* Coming Soon Section */}
        <section className="py-20 bg-white">
          <div className="max-w-4xl mx-auto px-6 lg:px-8 text-center">
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-3xl p-12 shadow-xl border border-blue-100">
              <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-8 shadow-lg">
                <Heart size={40} className="text-white" />
              </div>

              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Volunteer Registration Coming Soon!
              </h2>

              <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
                We're building an amazing volunteer registration system that will connect you with meaningful opportunities to help your community during disasters and emergencies.
              </p>

              {/* Features Preview */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="bg-white rounded-xl p-6 shadow-md border border-blue-100">
                  <Users className="w-8 h-8 text-blue-600 mx-auto mb-4" />
                  <h3 className="font-semibold text-gray-900 mb-2">Community Matching</h3>
                  <p className="text-sm text-gray-600">Get matched with volunteer opportunities in your local area</p>
                </div>

                <div className="bg-white rounded-xl p-6 shadow-md border border-blue-100">
                  <Award className="w-8 h-8 text-blue-600 mx-auto mb-4" />
                  <h3 className="font-semibold text-gray-900 mb-2">Skills Training</h3>
                  <p className="text-sm text-gray-600">Access training programs to develop emergency response skills</p>
                </div>

                <div className="bg-white rounded-xl p-6 shadow-md border border-blue-100">
                  <Star className="w-8 h-8 text-blue-600 mx-auto mb-4" />
                  <h3 className="font-semibold text-gray-900 mb-2">Impact Tracking</h3>
                  <p className="text-sm text-gray-600">Track your volunteer hours and see the impact you're making</p>
                </div>
              </div>

              {/* Notify Me Button */}
              <div className="bg-blue-600 text-white px-8 py-4 rounded-xl text-lg font-semibold shadow-lg inline-flex items-center">
                <CheckCircle size={20} className="mr-3" />
                Notify Me When Available
              </div>

              <p className="text-sm text-gray-500 mt-4">
                We'll send you an email when volunteer registration opens
              </p>
            </div>
          </div>
        </section>

        {/* Why Volunteer Section */}
        <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
          <div className="max-w-6xl mx-auto px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Why Volunteer With Us?
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Join a community of heroes who are making a real difference when it matters most
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="bg-white rounded-2xl p-8 shadow-lg border border-blue-100 hover:shadow-xl transition-shadow duration-300">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mb-6">
                  <Heart size={24} className="text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Make Real Impact</h3>
                <p className="text-gray-600 leading-relaxed">
                  Your efforts directly help communities recover from disasters and build resilience for the future.
                </p>
              </div>

              <div className="bg-white rounded-2xl p-8 shadow-lg border border-blue-100 hover:shadow-xl transition-shadow duration-300">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mb-6">
                  <Users size={24} className="text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Join Community</h3>
                <p className="text-gray-600 leading-relaxed">
                  Connect with like-minded people who share your passion for helping others and making a difference.
                </p>
              </div>

              <div className="bg-white rounded-2xl p-8 shadow-lg border border-blue-100 hover:shadow-xl transition-shadow duration-300">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mb-6">
                  <Award size={24} className="text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Develop Skills</h3>
                <p className="text-gray-600 leading-relaxed">
                  Gain valuable emergency response, leadership, and communication skills that benefit your career.
                </p>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default VolunteerRegister;
